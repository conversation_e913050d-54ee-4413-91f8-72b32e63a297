# 分類管理系統

本系統提供完整的三級分類管理功能，包括大分類、中分類和小分類的 CRUD 操作。

## 功能概述

### 分類層級結構
```
大分類 (Large Category)
├── 中分類 (Medium Category)
    ├── 小分類 (Small Category)
    └── 小分類 (Small Category)
└── 中分類 (Medium Category)
    └── 小分類 (Small Category)
```

### 頁面路徑
- **統一入口**: `/settings/categories` - 分類管理主頁面
- **大分類管理**: `/settings/categories/large`
- **中分類管理**: `/settings/categories/medium`
- **小分類管理**: `/settings/categories/small`

## 技術實現

### 檔案結構
```
app/
├── interfaces/dto/
│   └── category.dto.ts                    # 分類相關的 TypeScript 介面定義
├── services/api/
│   └── categoryApi.ts                     # 分類 API 服務
└── (dashboard)/settings/
    └── categories/
        ├── page.tsx                       # 分類管理入口頁面
        ├── large/
        │   └── page.tsx                   # 大分類管理頁面
        ├── medium/
        │   └── page.tsx                   # 中分類管理頁面
        └── small/
            └── page.tsx                   # 小分類管理頁面
```

### API 端點
根據 `CategoryAPI.md` 文檔實現：

#### 大分類 API
- `POST /LargeCategories` - 查詢大分類列表
- `GET /LargeCategories/{id}` - 取得大分類詳細資料
- `POST /LargeCategories` - 新增大分類
- `POST /LargeCategories/{id}` - 更新大分類
- `DELETE /LargeCategories/{id}` - 刪除大分類

#### 中分類 API
- `POST /MediumCategories` - 查詢中分類列表
- `GET /MediumCategories/{id}` - 取得中分類詳細資料
- `POST /MediumCategories` - 新增中分類
- `POST /MediumCategories/{id}` - 更新中分類
- `DELETE /MediumCategories/{id}` - 刪除中分類

#### 小分類 API
- `POST /SmallCategories` - 查詢小分類列表
- `GET /SmallCategories/{id}` - 取得小分類詳細資料
- `POST /SmallCategories` - 新增小分類
- `POST /SmallCategories/{id}` - 更新小分類
- `DELETE /SmallCategories/{id}` - 刪除小分類

## 功能特色

### 1. 完整的 CRUD 操作
- ✅ 新增分類
- ✅ 查詢分類列表（支援分頁、排序、篩選）
- ✅ 編輯分類
- ✅ 刪除分類

### 2. 分頁與排序
- 支援自訂每頁顯示筆數
- 支援多欄位排序
- 支援表頭篩選搜尋

### 3. 級聯選擇
- 中分類管理：自動載入大分類下拉選單
- 小分類管理：大分類選擇後自動載入對應的中分類選單

### 4. 表單驗證
- 必填欄位驗證
- 字串長度限制
- 數值範圍驗證

### 5. 使用者體驗
- 載入狀態指示
- 操作成功/失敗訊息提示
- 確認刪除對話框
- 響應式設計

## 使用方式

### 1. 建立分類體系
建議按照以下順序建立分類：
1. 先建立大分類
2. 在大分類下建立中分類
3. 在中分類下建立小分類

### 2. 管理分類
- 透過統一入口頁面 `/settings/categories` 選擇要管理的分類類型
- 每個分類管理頁面都提供完整的 CRUD 功能
- 支援批量操作和快速搜尋

### 3. 刪除注意事項
- 刪除大分類前，請先刪除其下的所有中分類
- 刪除中分類前，請先刪除其下的所有小分類
- 系統會在刪除時進行依賴檢查

## 技術細節

### 狀態管理
使用 React Hooks 進行狀態管理：
- `useState` 管理組件狀態
- `useEffect` 處理副作用
- `useCallback` 優化性能

### 表單處理
使用 Ant Design Form 組件：
- 表單驗證
- 動態表單項
- 表單重置

### API 整合
- 基於 `BaseApi` 類別的統一 API 服務
- 自動處理認證 Token
- 統一錯誤處理

### UI 組件
- 使用 `DataTable` 組件處理列表顯示
- 使用 Ant Design 組件庫
- 遵循專案 UI 約定

## 擴展性

### 新增分類類型
如需新增其他分類類型，可以：
1. 在 `category.dto.ts` 中定義新的介面
2. 在 `categoryApi.ts` 中新增對應的 API 方法
3. 創建新的管理頁面
4. 更新統一入口頁面

### 自訂欄位
可以透過修改 DTO 介面和表單組件來新增自訂欄位：
- 修改 `CreateXxxCategoryDto` 和 `UpdateXxxCategoryDto`
- 更新表單組件中的 Form.Item
- 調整表格欄位定義

## 注意事項

1. **權限控制**: 確保使用者有適當的權限進行分類管理操作
2. **資料一致性**: 刪除操作會檢查依賴關係，避免資料不一致
3. **效能考量**: 大量資料時建議使用分頁和篩選功能
4. **錯誤處理**: 所有 API 呼叫都有適當的錯誤處理機制 