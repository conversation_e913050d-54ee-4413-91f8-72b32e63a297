'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight } from 'lucide-react';

// 定義 MenuTree 介面
interface MenuTree {
  Name: string;
  Id: string;
  Selected: boolean;
  Children: MenuTree[];
}

interface BreadcrumbItem {
  name: string;
  href: string;
}

// 從 localStorage 獲取選單數據
const getMenuTrees = (): MenuTree[] => {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return [];
    
    const userData = JSON.parse(userStr);
    if (!userData.currentUser?.MenuTrees) return [];
    
    return userData.currentUser.MenuTrees;
  } catch (error) {
    console.error('Error parsing menu trees:', error);
    return [];
  }
};

// --- 新增：優化輔助函數，建立查找映射 ---
const buildMenuMap = (menus: MenuTree[]): { [id: string]: MenuTree } => {
  const map: { [id: string]: MenuTree } = {};
  if (!menus) return map; // 添加保護
  for (const menu of menus) {
    map[menu.Id] = menu;
  }
  return map;
};

// --- 修改：根據路徑查找麵包屑項目 (優化版) ---
const findBreadcrumbItems = (path: string, menuTrees: MenuTree[]): BreadcrumbItem[] => {
  // 報表路徑映射
  const segmentMap: { [key: string]: string } = {
    ReportGeneration: 'reports',
    DailyReport: 'daily',
    WeeklyReport: 'weekly',
    BusinessReport: 'management',
    MediaBenefitAnalysis: 'media-benefit-analysis',
  };
  const parts = path.split('/').filter(Boolean);
  const items: BreadcrumbItem[] = [];
  let currentPath = '';
  let currentLevelMenus = menuTrees; // 從頂層開始

  for (const part of parts) {
    currentPath += `/${part}`;
    // 在當前層級尋找匹配的 menuTree，考慮 segmentMap 映射
    const matchingMenu = currentLevelMenus.find(menu => {
      const segment = segmentMap[menu.Id] || menu.Id;
      return segment === part;
    });
    if (!matchingMenu) {
      break; // 找不到對應選單則停止
    }
    items.push({ name: matchingMenu.Name, href: currentPath });
    currentLevelMenus = matchingMenu.Children;
  }
  return items;
};
// --- 結束修改 ---

export default function Breadcrumb() {
  const pathname = usePathname();
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);

  useEffect(() => {
    const menuTrees = getMenuTrees();
    const items = findBreadcrumbItems(pathname, menuTrees);
    setBreadcrumbs(items);
  }, [pathname]);

  if (breadcrumbs.length <= 1) return null;

  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-600">
      {breadcrumbs.map((item, index) => (
        <React.Fragment key={item.href}>
          {index > 0 && (
            <ChevronRight className="w-4 h-4 text-gray-400" />
          )}
          {index === breadcrumbs.length - 1 ? (
            <span className="text-gray-900">{item.name}</span>
          ) : (
            <Link
              href={item.href}
              className="hover:text-blue-600 transition-colors"
            >
              {item.name}
            </Link>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
} 