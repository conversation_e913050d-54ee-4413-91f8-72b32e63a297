import { BaseApi } from './baseApi';
import {
  BaseCrmOptionItem,
  CrmOptionDetail,
  GetCrmOptionsParams,
  CrmOptionListResponse,
  CreateCrmOptionDto,
  UpdateCrmOptionDto,
  CreateCrmOptionResponse,
  GetCrmOptionDropdownParams,
  CrmOptionDropdownItem,
  CrmOptionTypeDropdownItem,
  CrmOptionQueryDto
} from '../../interfaces/dto/crm-option.dto';

class CrmOptionApi extends BaseApi {
  constructor() {
    super('CrmOptions'); // API 路徑前綴為 CrmOptions
  }

  /**
   * 查詢CRM選項列表 (分頁)
   * POST /api/CrmOptions/GetCrmOptions
   */
  async getCrmOptions(query: CrmOptionQueryDto): Promise<CrmOptionListResponse> {
    // 轉換 BaseQueryParams 格式為新 API 格式
    const apiRequest: GetCrmOptionsParams = {
      SiteCode: query.SiteCode,
      CrmOptionTypeId: query.CrmOptionTypeId,
      OptionValue: query.OptionValue,
      IsActive: query.IsActive,
      PageIndex: query.PageIndex || 1,
      PageSize: query.NumberOfPperPage || 10
    };
    
    const response = await this.post<CrmOptionListResponse>('/GetCrmOptions', apiRequest);
    
    // 檢查回應結構 - API 回應可能包裝在 body 屬性中
    const actualData = (response as any).body || response;
    return actualData;
  }

  /**
   * 取得CRM選項詳細資料
   * GET /api/CrmOptions/GetCrmOption/{siteCrmOptionId}
   */
  async getCrmOptionById(siteCrmOptionId: number): Promise<CrmOptionDetail> {
    const response = await this.get<CrmOptionDetail>(`/GetCrmOption/${siteCrmOptionId}`);
    
    // 檢查回應結構 - API 回應可能包裝在 body 屬性中
    const actualData = (response as any).body || response;
    return actualData;
  }

  /**
   * 新增CRM選項
   * POST /api/CrmOptions/CreateCrmOption
   */
  async createCrmOption(data: CreateCrmOptionDto): Promise<CreateCrmOptionResponse> {
    const response = await this.post<CreateCrmOptionResponse>('/CreateCrmOption', data);
    
    // 檢查回應結構 - API 回應可能包裝在 body 屬性中
    const actualData = (response as any).body || response;
    return actualData;
  }

  /**
   * 更新CRM選項
   * PUT /api/CrmOptions/UpdateCrmOption/{siteCrmOptionId}
   */
  async updateCrmOption(siteCrmOptionId: number, data: UpdateCrmOptionDto): Promise<void> {
    await this.put<void>(`/UpdateCrmOption/${siteCrmOptionId}`, data);
  }

  /**
   * 刪除CRM選項
   * DELETE /api/CrmOptions/DeleteCrmOption/{siteCrmOptionId}
   */
  async deleteCrmOption(siteCrmOptionId: number): Promise<void> {
    await this.delete<void>(`/DeleteCrmOption/${siteCrmOptionId}`);
  }

  /**
   * 取得CRM選項下拉選單
   * POST /api/CrmOptions/GetCrmOptionDropdown
   */
  async getCrmOptionDropdown(params: GetCrmOptionDropdownParams): Promise<CrmOptionDropdownItem[]> {
    const response = await this.post<CrmOptionDropdownItem[]>('/GetCrmOptionDropdown', params);
    
    // 檢查回應結構 - API 回應可能包裝在 body 屬性中
    const actualData = (response as any).body || response;
    const dataArray = Array.isArray(actualData) ? actualData : [];
    return dataArray;
  }

  /**
   * 取得CRM選項類型下拉選單
   * GET /api/CrmOptions/GetCrmOptionTypeDropdown
   */
  async getCrmOptionTypeDropdown(): Promise<CrmOptionTypeDropdownItem[]> {
    const response = await this.get<CrmOptionTypeDropdownItem[]>('/GetCrmOptionTypeDropdown');
    
    // 檢查回應結構 - API 回應可能包裝在 body 屬性中
    const actualData = (response as any).body || response;
    const dataArray = Array.isArray(actualData) ? actualData : [];
    return dataArray;
  }
}

// 導出單例實例
export const crmOptionApi = new CrmOptionApi();
