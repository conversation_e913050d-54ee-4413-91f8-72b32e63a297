'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Form, Input, InputNumber, Switch, Button, Modal, Popconfirm, Space, message, Select } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import DataTable from '../../../../components/DataTable';
import { crmOptionApi } from '../../../../services/api/crmOptionApi';
import { siteApi } from '../../../../services/api/siteApi';
import {
  BaseCrmOptionItem,
  CrmOptionQueryDto,
  CreateCrmOptionDto,
  UpdateCrmOptionDto,
  CrmOptionDetail,
  CrmOptionTypeDropdownItem
} from '../../../../interfaces/dto/crm-option.dto';
import { DropdownItem, SearchTermInfo } from '../../../../interfaces/dto/common.dto';
import { SorterResult } from 'antd/es/table/interface';

export default function CrmOptionsPage() {
  const [crmOptions, setCrmOptions] = useState<BaseCrmOptionItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'add' | 'edit'>('add');
  const [editingOption, setEditingOption] = useState<CrmOptionDetail | null>(null);
  const [messageApi, contextHolder] = message.useMessage();

  // 下拉選單資料
  const [siteOptions, setSiteOptions] = useState<DropdownItem[]>([]);
  const [optionTypeOptions, setOptionTypeOptions] = useState<CrmOptionTypeDropdownItem[]>([]);

  // 分頁和排序狀態
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(null);
  const [searchInfos, setSearchInfos] = useState<SearchTermInfo[] | undefined>(undefined);

  // 篩選狀態
  const [selectedSiteCode, setSelectedSiteCode] = useState<string | undefined>(undefined);
  const [selectedOptionTypeId, setSelectedOptionTypeId] = useState<number | undefined>(undefined);

  // 預設新增表單值
  const defaultAddFormValues: Partial<CreateCrmOptionDto> = {
    SiteCode: '',
    CrmOptionTypeId: undefined,
    OptionValue: '',
    SortOrder: 1,
    IsActive: true,
  };

  // 載入下拉選單資料
  const loadDropdownData = useCallback(async () => {
    try {
      // 載入案場下拉選單
      const siteResponse = await siteApi.getSiteDropdownList();
      const siteData = siteResponse.body || [];
      setSiteOptions(siteData);

      // 載入CRM選項類型下拉選單
      const optionTypeData = await crmOptionApi.getCrmOptionTypeDropdown();
      setOptionTypeOptions(optionTypeData);
    } catch (error: any) {
      messageApi.error('載入下拉選單資料失敗：' + (error.message || '未知錯誤'));
    }
  }, [messageApi]);

  // 獲取資料
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const apiParams: CrmOptionQueryDto = {
        UsingPaging: true,
        PageIndex: currentPage,
        NumberOfPperPage: pageSize,
        SortOrderInfos: sortField && sortOrder
          ? [{ SortField: sortField, SortOrder: sortOrder === 'ascend' ? 'asc' : 'desc' }]
          : undefined,
        SearchTermInfos: searchInfos,
        SiteCode: selectedSiteCode,
        CrmOptionTypeId: selectedOptionTypeId,
      };

      console.log('API 請求參數:', apiParams);
      const response = await crmOptionApi.getCrmOptions(apiParams);
      console.log('API 回應:', response);

      // 檢查回應結構 - API 回應可能包裝在 body 屬性中
      const actualData = (response as any).body || response;
      console.log('實際資料:', actualData);

      const dataArray = actualData.Details || actualData.Detail || [];
      const totalCount = actualData.TotalCount || actualData.RecordCount || dataArray.length;

      console.log('解析後的資料陣列:', dataArray);
      console.log('總筆數:', totalCount);

      setCrmOptions(dataArray);
      setTotalCount(totalCount);
    } catch (error: any) {
      console.error('API 錯誤:', error);
      messageApi.error(error.message || '獲取CRM選項列表失敗');
      setCrmOptions([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, sortField, sortOrder, searchInfos, selectedSiteCode, selectedOptionTypeId, messageApi]);

  useEffect(() => {
    loadDropdownData();
  }, [loadDropdownData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 表格欄位定義
  const columns = [
    { 
      title: '案場', 
      dataIndex: 'SiteName', 
      key: 'SiteName', 
      sorter: true, 
      allowSearch: true,
      width: 150
    },
    { 
      title: '選項類型', 
      dataIndex: 'CrmOptionTypeName', 
      key: 'CrmOptionTypeName', 
      sorter: true, 
      allowSearch: true,
      width: 120
    },
    { 
      title: '選項值', 
      dataIndex: 'OptionValue', 
      key: 'OptionValue', 
      sorter: true, 
      allowSearch: true 
    },
    { 
      title: '排序', 
      dataIndex: 'SortOrder', 
      key: 'SortOrder', 
      sorter: true,
      width: 80
    },
    { 
      title: '狀態', 
      dataIndex: 'IsActive', 
      key: 'IsActive',
      width: 80,
      render: (isActive: boolean) => (
        <span className={isActive ? 'text-green-600' : 'text-red-600'}>
          {isActive ? '啟用' : '停用'}
        </span>
      )
    },
    { 
      title: '建立時間', 
      dataIndex: 'CreateTime', 
      key: 'CreateTime', 
      sorter: true,
      width: 120,
      render: (text: string) => text ? new Date(text).toLocaleDateString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: BaseCrmOptionItem) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定刪除此CRM選項嗎？"
            description="刪除後將無法復原。"
            onConfirm={() => handleDelete(record.SiteCrmOptionId)}
            okText="確定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 分頁處理
  const handlePageChange = (page: number, pageSizeParam: number) => {
    setCurrentPage(page);
    setPageSize(pageSizeParam);
  };

  // 排序處理
  const handleSortChange = (sorter: SorterResult<BaseCrmOptionItem> | SorterResult<BaseCrmOptionItem>[]) => {
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    const newSortField = (currentSorter?.field as string) || null;
    const newSortOrder = currentSorter?.order || null;

    if (newSortField !== sortField || newSortOrder !== sortOrder) {
      setSortField(newSortField);
      setSortOrder(newSortOrder);
      if (currentPage !== 1) setCurrentPage(1);
    }
  };

  // 篩選處理
  const handleFilterChange = (currentSearchInfos: SearchTermInfo[]) => {
    setSearchInfos(currentSearchInfos.length > 0 ? currentSearchInfos : undefined);
    if (currentPage !== 1) setCurrentPage(1);
  };

  // 顯示新增模態框
  const showAddModal = () => {
    setModalType('add');
    setEditingOption(null);
    setIsModalOpen(true);
  };

  // 顯示編輯模態框
  const showEditModal = async (record: BaseCrmOptionItem) => {
    setModalType('edit');
    try {
      setLoading(true);
      const detailResponse = await crmOptionApi.getCrmOptionById(record.SiteCrmOptionId);
      setEditingOption(detailResponse);
      setIsModalOpen(true);
    } catch (error: any) {
      messageApi.error(error.message || '獲取CRM選項詳細資料失敗');
    } finally {
      setLoading(false);
    }
  };

  // 關閉模態框
  const handleCancel = () => {
    setIsModalOpen(false);
    setEditingOption(null);
  };

  // 刪除處理
  const handleDelete = async (optionId: number) => {
    try {
      setLoading(true);
      await crmOptionApi.deleteCrmOption(optionId);
      messageApi.success('刪除CRM選項成功');
      fetchData();
    } catch (error: any) {
      messageApi.error(error.message || '刪除CRM選項失敗');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {contextHolder}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">CRM選項管理</h1>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showAddModal}
          >
            新增CRM選項
          </Button>
        </div>

        {/* 篩選區域 */}
        <div className="mb-4 p-4 bg-gray-50 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">案場</label>
              <Select
                placeholder="選擇案場"
                allowClear
                value={selectedSiteCode}
                onChange={(value) => {
                  setSelectedSiteCode(value);
                  if (currentPage !== 1) setCurrentPage(1);
                }}
                style={{ width: '100%' }}
              >
                {siteOptions.map(site => (
                  <Select.Option key={site.Value} value={site.Value}>
                    {site.Name}
                  </Select.Option>
                ))}
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">選項類型</label>
              <Select
                placeholder="選擇選項類型"
                allowClear
                value={selectedOptionTypeId}
                onChange={(value) => {
                  setSelectedOptionTypeId(value);
                  if (currentPage !== 1) setCurrentPage(1);
                }}
                style={{ width: '100%' }}
              >
                {optionTypeOptions.map(type => (
                  <Select.Option key={type.Value} value={type.Value}>
                    {type.Name}
                  </Select.Option>
                ))}
              </Select>
            </div>
          </div>
        </div>

        <DataTable<BaseCrmOptionItem>
          columns={columns}
          dataSource={crmOptions}
          loading={loading}
          total={totalCount}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onSort={handleSortChange}
          onFilter={handleFilterChange}
          rowKey="SiteCrmOptionId"
        />

        <CrmOptionFormModal
          isOpen={isModalOpen}
          modalType={modalType}
          editingOption={editingOption}
          defaultValues={defaultAddFormValues}
          siteOptions={siteOptions}
          optionTypeOptions={optionTypeOptions}
          onCancel={handleCancel}
          onSuccess={fetchData}
          messageApi={messageApi}
        />
      </div>
    </div>
  );
}

// 獨立的表單模態框元件
interface CrmOptionFormModalProps {
  isOpen: boolean;
  modalType: 'add' | 'edit';
  editingOption: CrmOptionDetail | null;
  defaultValues: Partial<CreateCrmOptionDto>;
  siteOptions: DropdownItem[];
  optionTypeOptions: CrmOptionTypeDropdownItem[];
  onCancel: () => void;
  onSuccess: () => void;
  messageApi: any;
}

function CrmOptionFormModal({
  isOpen,
  modalType,
  editingOption,
  defaultValues,
  siteOptions,
  optionTypeOptions,
  onCancel,
  onSuccess,
  messageApi
}: CrmOptionFormModalProps) {
  const [form] = Form.useForm<CreateCrmOptionDto | UpdateCrmOptionDto>();
  const [loading, setLoading] = useState(false);

  // 當模態框打開且是編輯模式時，設置表單值
  useEffect(() => {
    if (isOpen) {
      if (modalType === 'edit' && editingOption) {
        form.setFieldsValue({
          SiteCode: editingOption.SiteCode,
          CrmOptionTypeId: editingOption.CrmOptionTypeId,
          OptionValue: editingOption.OptionValue,
          SortOrder: editingOption.SortOrder,
          IsActive: editingOption.IsActive,
        });
      } else {
        form.setFieldsValue(defaultValues as any);
      }
    }
  }, [isOpen, modalType, editingOption, defaultValues, form]);

  // 提交表單
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (modalType === 'add') {
        await crmOptionApi.createCrmOption(values as CreateCrmOptionDto);
        messageApi.success('新增CRM選項成功');
      } else {
        if (!editingOption) {
          messageApi.error('編輯資料不存在');
          return;
        }
        const updateData: UpdateCrmOptionDto = {
          OptionValue: values.OptionValue,
          SortOrder: values.SortOrder,
          IsActive: values.IsActive,
        };
        await crmOptionApi.updateCrmOption(editingOption.SiteCrmOptionId, updateData);
        messageApi.success('更新CRM選項成功');
      }

      form.resetFields();
      onCancel();
      onSuccess();
    } catch (error: any) {
      if (error.errorFields) {
        // 表單驗證錯誤
        return;
      }
      messageApi.error(error.message || `${modalType === 'add' ? '新增' : '更新'}CRM選項失敗`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={modalType === 'add' ? '新增CRM選項' : '編輯CRM選項'}
      open={isOpen}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          {modalType === 'add' ? '新增' : '更新'}
        </Button>,
      ]}
      width={600}
      destroyOnClose
    >
      {isOpen && (
        <Form
          form={form}
          layout="vertical"
          initialValues={defaultValues}
        >
          <Form.Item
            label="案場"
            name="SiteCode"
            rules={[
              { required: true, message: '請選擇案場' }
            ]}
          >
            <Select
              placeholder="請選擇案場"
              disabled={modalType === 'edit'} // 編輯時不允許修改案場
            >
              {siteOptions.map(site => (
                <Select.Option key={site.Value} value={site.Value}>
                  {site.Name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="選項類型"
            name="CrmOptionTypeId"
            rules={[
              { required: true, message: '請選擇選項類型' }
            ]}
          >
            <Select
              placeholder="請選擇選項類型"
              disabled={modalType === 'edit'} // 編輯時不允許修改選項類型
            >
              {optionTypeOptions.map(type => (
                <Select.Option key={type.Value} value={type.Value}>
                  <div>
                    <div>{type.Name}</div>
                    <div className="text-xs text-gray-500">{type.Description}</div>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="選項值"
            name="OptionValue"
            rules={[
              { required: true, message: '請輸入選項值' },
              { max: 200, message: '選項值不能超過200個字元' }
            ]}
          >
            <Input placeholder="請輸入選項值" />
          </Form.Item>

          <Form.Item
            label="排序"
            name="SortOrder"
            rules={[
              { required: true, message: '請輸入排序數值' },
              { type: 'number', min: 0, message: '排序數值不能小於0' }
            ]}
          >
            <InputNumber
              placeholder="請輸入排序數值"
              min={0}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            label="狀態"
            name="IsActive"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="啟用"
              unCheckedChildren="停用"
            />
          </Form.Item>
        </Form>
      )}
    </Modal>
  );
}
