# CRM選項管理系統 - 功能總結

## 📋 系統概述

本系統提供完整的CRM選項管理功能，允許不同案場設定自己的客戶關係管理下拉選單選項。系統支援多種選項類型（如需求坪數、需求格局、預算範圍等），每個案場可以針對不同的選項類型建立自己的選項值。

## 🏗️ 系統架構

### 資料表結構
1. **CrmOptionTypes** - CRM選項類型主表
   - 儲存選項類型的基本資訊（如需求坪數、需求格局等）
   - 全系統共用，不分案場

2. **SiteCrmOptions** - 案場CRM選項表
   - 儲存各案場在不同選項類型下的具體選項值
   - 支援排序和啟用/停用功能

### 業務邏輯層
- **CrmOptionService** - 核心業務邏輯服務
- **Input/Output Models** - 完整的輸入輸出模型
- **驗證機制** - 資料完整性和業務規則驗證

### API控制器層
- **CrmOptionsController** - RESTful API端點
- **路由設計** - 清晰的API路由結構
- **錯誤處理** - 統一的錯誤回應格式

## 🚀 核心功能

### 1. CRM選項管理 (CRUD)
- ✅ **新增選項** - 為指定案場和選項類型建立新的選項值
- ✅ **查詢選項** - 支援分頁、篩選的選項列表查詢
- ✅ **更新選項** - 修改選項值、排序和啟用狀態
- ✅ **刪除選項** - 移除不需要的選項
- ✅ **詳細查詢** - 取得單一選項的完整資訊

### 2. 下拉選單支援
- ✅ **案場選項下拉** - 根據案場和選項類型取得下拉選單
- ✅ **選項類型下拉** - 取得所有可用的選項類型
- ✅ **排序支援** - 自訂下拉選單中選項的顯示順序
- ✅ **啟用控制** - 可選擇只顯示啟用的選項

### 3. 業務規則驗證
- ✅ **唯一性檢查** - 同一案場、同一選項類型下選項值不可重複
- ✅ **關聯性驗證** - 確保案場代碼和選項類型ID存在
- ✅ **資料完整性** - 必填欄位和格式驗證
- ✅ **權限控制** - 基於認證的API存取控制

## 📊 API端點總覽

| 方法 | 端點 | 功能 | 方法名稱 | 說明 |
|------|------|------|----------|------|
| POST | `/api/CrmOptions/GetCrmOptionList` | 查詢選項列表 | `GetCrmOptionListAsync` | 支援分頁和多條件篩選 |
| GET | `/api/CrmOptions/GetCrmOptionById/{id}` | 取得選項詳情 | `GetCrmOptionByIdAsync` | 包含完整的選項資訊 |
| POST | `/api/CrmOptions/CreateCrmOption` | 新增選項 | `CreateCrmOptionAsync` | 建立新的CRM選項 |
| PUT | `/api/CrmOptions/UpdateCrmOption/{id}` | 更新選項 | `UpdateCrmOptionAsync` | 修改現有選項資訊 |
| DELETE | `/api/CrmOptions/DeleteCrmOption/{id}` | 刪除選項 | `DeleteCrmOptionAsync` | 移除指定選項 |
| POST | `/api/CrmOptions/GetCrmOptionDropdown` | 取得選項下拉 | `GetCrmOptionDropdownAsync` | 前端下拉選單使用 |
| GET | `/api/CrmOptions/GetCrmOptionTypeDropdown` | 取得類型下拉 | `GetCrmOptionTypeDropdownAsync` | 選項類型選擇使用 |

## 🎯 使用場景

### 場景1: 案場初始化
1. 取得可用的選項類型 (`GET /api/CrmOptions/GetCrmOptionTypeDropdown`)
2. 為案場建立各種選項類型的選項值 (`POST /api/CrmOptions/CreateCrmOption`)
3. 設定選項的排序和啟用狀態

### 場景2: 客戶資料輸入
1. 根據案場和選項類型取得下拉選單 (`POST /api/CrmOptions/GetCrmOptionDropdown`)
2. 客戶選擇相應的選項值
3. 儲存客戶偏好資料

### 場景3: 選項維護
1. 查詢現有選項 (`POST /api/CrmOptions/GetCrmOptionList`)
2. 更新選項內容或排序 (`PUT /api/CrmOptions/UpdateCrmOption/{id}`)
3. 停用過時的選項或新增新選項

## 💡 設計特色

### 1. 案場隔離
- 每個案場擁有獨立的選項設定
- 不同案場間的選項互不影響
- 支援案場特色化的客戶管理需求

### 2. 靈活的選項類型
- 支援多種選項類型（坪數、格局、預算等）
- 可擴展的選項類型系統
- 統一的選項管理介面

### 3. 完整的生命週期管理
- 從建立到刪除的完整CRUD操作
- 軟刪除支援（透過IsActive控制）
- 詳細的異動記錄（建立者、更新者、時間戳記）

### 4. 前端友善的API設計
- RESTful API設計原則
- 清晰的請求/回應格式
- 完整的錯誤處理和訊息

## 🔧 技術實現

### 後端技術
- **ASP.NET Core 6.0** - Web API框架
- **Entity Framework Core** - ORM資料存取
- **PostgreSQL** - 資料庫系統
- **依賴注入** - 服務生命週期管理

### 程式碼品質
- **完整的XML註解** - API文檔自動生成
- **輸入驗證** - Data Annotations驗證
- **異常處理** - 統一的錯誤處理機制
- **非同步程式設計** - 提升系統效能

### 安全性考量
- **認證控制** - 繼承AuthenticatedController
- **輸入驗證** - 防止惡意輸入
- **SQL注入防護** - 使用參數化查詢
- **業務邏輯驗證** - 多層次的資料驗證

## 📈 效益與價值

### 1. 業務效益
- **提升客戶管理效率** - 標準化的選項管理
- **支援多案場營運** - 彈性的案場設定
- **改善資料品質** - 統一的選項格式

### 2. 技術效益
- **降低維護成本** - 統一的API介面
- **提升開發效率** - 可重用的服務元件
- **增強系統穩定性** - 完整的錯誤處理

### 3. 使用者體驗
- **直觀的操作介面** - 清晰的API設計
- **快速的回應時間** - 最佳化的查詢效能
- **一致的操作體驗** - 標準化的互動模式

## 🚀 未來擴展

### 可能的增強功能
1. **選項模板** - 預設選項模板快速套用
2. **批次操作** - 批次建立/更新/刪除選項
3. **選項統計** - 選項使用頻率分析
4. **版本控制** - 選項變更歷史追蹤
5. **匯入匯出** - Excel格式的選項資料交換

### 整合可能性
1. **客戶管理系統** - 與CRM系統深度整合
2. **報表系統** - 選項使用情況報表
3. **權限系統** - 細粒度的選項管理權限
4. **通知系統** - 選項變更通知機制

## 📚 相關文檔

- [CrmOptionAPI.md](CrmOptionAPI.md) - 完整的API使用文檔
- [CrmOptionAPI_Test.md](CrmOptionAPI_Test.md) - API測試指南
- [DBSchema.md](DBSchema.md) - 資料庫架構說明

---

**開發完成日期**: 2024年6月16日  
**版本**: v1.0.0  
**開發者**: Augment Agent
