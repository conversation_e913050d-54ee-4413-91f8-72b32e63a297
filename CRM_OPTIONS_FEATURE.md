# CRM選項管理功能

## 功能概述

本功能為A-Life系統新增了CRM選項管理模組，允許各案場設定專屬的下拉選單選項，包括需求坪數、需求格局、預算範圍等。

## 新增的文件

### 1. DTO接口文件
- `app/interfaces/dto/crm-option.dto.ts` - CRM選項相關的TypeScript接口定義

### 2. API服務文件
- `app/services/api/crmOptionApi.ts` - CRM選項API服務類，提供完整的CRUD操作

### 3. 頁面組件
- `app/(dashboard)/settings/categories/crm-options/page.tsx` - CRM選項管理頁面

### 4. 更新的文件
- `app/(dashboard)/settings/categories/page.tsx` - 添加了CRM選項管理的導航卡片

## 功能特點

### 1. 案場隔離
- 每個案場可以設定自己的CRM選項
- 不同案場間的選項互不影響
- 支援案場特色化的客戶管理需求

### 2. 多種選項類型
- 需求坪數（如：20-30坪、30-40坪）
- 需求格局（如：2房2廳、3房2廳）
- 預算範圍（如：1000-1500萬、1500-2000萬）
- 可擴展支援更多選項類型

### 3. 完整的CRUD操作
- **新增**：為指定案場和選項類型建立新的選項值
- **查詢**：支援分頁、排序、篩選的選項列表查詢
- **更新**：修改選項值、排序和啟用狀態
- **刪除**：移除不需要的選項

### 4. 進階功能
- 分頁顯示，支援大量資料
- 多欄位排序
- 即時搜尋篩選
- 案場和選項類型篩選
- 啟用/停用狀態管理
- 自訂排序順序

## API端點

基於後端提供的CRM選項API，前端實現了以下功能：

| 方法 | 端點 | 功能 |
|------|------|------|
| POST | `/api/CrmOptions/GetCrmOptionList` | 查詢選項列表（分頁） |
| GET | `/api/CrmOptions/GetCrmOptionById/{id}` | 取得選項詳情 |
| POST | `/api/CrmOptions/CreateCrmOption` | 新增選項 |
| PUT | `/api/CrmOptions/UpdateCrmOption/{id}` | 更新選項 |
| DELETE | `/api/CrmOptions/DeleteCrmOption/{id}` | 刪除選項 |
| POST | `/api/CrmOptions/GetCrmOptionDropdown` | 取得選項下拉選單 |
| GET | `/api/CrmOptions/GetCrmOptionTypeDropdown` | 取得選項類型下拉選單 |

## 使用方式

### 1. 訪問功能
1. 登入系統後，導航至「設定」→「分類管理」
2. 點擊「CRM選項管理」卡片
3. 進入CRM選項管理頁面

### 2. 新增CRM選項
1. 點擊「新增CRM選項」按鈕
2. 選擇案場（必填）
3. 選擇選項類型（必填）
4. 輸入選項值（必填）
5. 設定排序順序
6. 設定啟用狀態
7. 點擊「新增」完成

### 3. 編輯CRM選項
1. 在列表中找到要編輯的選項
2. 點擊「編輯」按鈕
3. 修改選項值、排序或狀態
4. 點擊「更新」完成

### 4. 篩選和搜尋
- 使用頂部的案場和選項類型下拉選單進行篩選
- 使用表格欄位的搜尋功能進行即時搜尋
- 點擊欄位標題進行排序

## 技術實現

### 1. 前端技術棧
- **React 18** - 主要框架
- **Next.js 15** - 全端框架
- **TypeScript** - 類型安全
- **Ant Design** - UI組件庫
- **Tailwind CSS** - 樣式框架

### 2. 架構設計
- **分層架構**：DTO → API Service → Page Component
- **類型安全**：完整的TypeScript接口定義
- **可重用性**：基於BaseApi的統一API服務
- **一致性**：遵循現有的設計模式和代碼風格

### 3. 錯誤處理
- API錯誤統一處理和顯示
- 表單驗證錯誤提示
- 網路錯誤重試機制
- 用戶友善的錯誤訊息

## 業務價值

### 1. 提升客戶管理效率
- 標準化的選項管理
- 快速的資料輸入
- 一致的資料格式

### 2. 支援多案場營運
- 彈性的案場設定
- 獨立的選項管理
- 個性化的客戶需求分類

### 3. 改善資料品質
- 統一的選項格式
- 避免重複和錯誤輸入
- 結構化的客戶資料

## 未來擴展

### 1. 可能的增強功能
- 選項模板功能（快速套用常用選項）
- 批次操作（批次新增/更新/刪除）
- 選項使用統計和分析
- 匯入匯出功能

### 2. 整合可能性
- 與客戶管理系統深度整合
- 與報表系統整合
- 與權限系統整合
- 與通知系統整合

## 注意事項

1. **資料一致性**：刪除選項前請確認沒有客戶資料使用該選項
2. **權限控制**：確保只有授權用戶可以修改CRM選項
3. **效能考量**：大量資料時建議使用分頁和篩選功能
4. **備份建議**：重要操作前建議備份相關資料

---

**開發完成日期**: 2024年6月16日  
**版本**: v1.0.0  
**開發者**: Augment Agent
