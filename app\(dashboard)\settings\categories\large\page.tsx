'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Form, Input, InputNumber, Switch, Button, Modal, Popconfirm, Space, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import DataTable from '../../../../components/DataTable';
import BackButton from '../../../../components/common/BackButton';
import { categoryApi } from '../../../../services/api/categoryApi';
import {
  LargeCategoryListItem,
  LargeCategoryQueryDto,
  CreateLargeCategoryDto,
  UpdateLargeCategoryDto,
  LargeCategoryDetail
} from '../../../../interfaces/dto/category.dto';
import { SearchTermInfo } from '../../../../interfaces/dto/common.dto';
import { SorterResult } from 'antd/es/table/interface';

export default function LargeCategoriesPage() {
  const [categories, setCategories] = useState<LargeCategoryListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'add' | 'edit'>('add');
  const [editingCategory, setEditingCategory] = useState<LargeCategoryDetail | null>(null);
  const [messageApi, contextHolder] = message.useMessage();

  // 分頁和排序狀態
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>(null);
  const [searchInfos, setSearchInfos] = useState<SearchTermInfo[] | undefined>(undefined);

  // 預設新增表單值
  const defaultAddFormValues: Partial<CreateLargeCategoryDto> = {
    Name: '',
    Description: '',
    SortOrder: 1,
    IsActive: true,
  };

  // 獲取資料
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const apiParams: LargeCategoryQueryDto = {
        UsingPaging: true,
        PageIndex: currentPage,
        NumberOfPperPage: pageSize,
        SortOrderInfos: sortField && sortOrder
          ? [{ SortField: sortField, SortOrder: sortOrder === 'ascend' ? 'asc' : 'desc' }]
          : undefined,
        SearchTermInfos: searchInfos,
      };

      const response = await categoryApi.getLargeCategories(apiParams);
      
      // 檢查回應結構 - API 回應可能包裝在 body 屬性中
      const actualData = (response as any).body || response;
      const dataArray = actualData.Detail || actualData.Details || [];
      
      setCategories(dataArray);
      setTotalCount(actualData.RecordCount || actualData.TotalCount || dataArray.length);
    } catch (error: any) {
      messageApi.error(error.message || '獲取大分類列表失敗');
      setCategories([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, sortField, sortOrder, searchInfos, messageApi]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 表格欄位定義
  const columns = [
    { 
      title: '分類名稱', 
      dataIndex: 'Name', 
      key: 'Name', 
      sorter: true, 
      allowSearch: true 
    },
    { 
      title: '描述', 
      dataIndex: 'Description', 
      key: 'Description', 
      allowSearch: true,
      render: (text: string) => text || '-'
    },
    { 
      title: '排序', 
      dataIndex: 'SortOrder', 
      key: 'SortOrder', 
      sorter: true,
      width: 100
    },
    { 
      title: '狀態', 
      dataIndex: 'IsActive', 
      key: 'IsActive',
      width: 100,
      render: (isActive: boolean) => (
        <span className={isActive ? 'text-green-600' : 'text-red-600'}>
          {isActive ? '啟用' : '停用'}
        </span>
      )
    },
    { 
      title: '建立時間', 
      dataIndex: 'CreateTime', 
      key: 'CreateTime', 
      sorter: true,
      render: (text: string) => text ? new Date(text).toLocaleDateString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: LargeCategoryListItem) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定刪除此大分類嗎？"
            description="刪除後將無法復原，且會影響相關的中分類和小分類。"
            onConfirm={() => handleDelete(record.LargeCategoryId)}
            okText="確定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 分頁處理
  const handlePageChange = (page: number, pageSizeParam: number) => {
    setCurrentPage(page);
    setPageSize(pageSizeParam);
  };

  // 排序處理
  const handleSortChange = (sorter: SorterResult<LargeCategoryListItem> | SorterResult<LargeCategoryListItem>[]) => {
    const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter;
    const newSortField = (currentSorter?.field as string) || null;
    const newSortOrder = currentSorter?.order || null;

    if (newSortField !== sortField || newSortOrder !== sortOrder) {
      setSortField(newSortField);
      setSortOrder(newSortOrder);
      if (currentPage !== 1) setCurrentPage(1);
    }
  };

  // 篩選處理
  const handleFilterChange = (currentSearchInfos: SearchTermInfo[]) => {
    setSearchInfos(currentSearchInfos.length > 0 ? currentSearchInfos : undefined);
    if (currentPage !== 1) setCurrentPage(1);
  };

  // 顯示新增模態框
  const showAddModal = () => {
    setModalType('add');
    setEditingCategory(null);
    setIsModalOpen(true);
  };

  // 顯示編輯模態框
  const showEditModal = async (record: LargeCategoryListItem) => {
    setModalType('edit');
    try {
      setLoading(true);
      const detailResponse = await categoryApi.getLargeCategoryById(record.LargeCategoryId);
      setEditingCategory(detailResponse);
      setIsModalOpen(true);
    } catch (error: any) {
      messageApi.error(error.message || '獲取大分類詳細資料失敗');
    } finally {
      setLoading(false);
    }
  };

  // 關閉模態框
  const handleCancel = () => {
    setIsModalOpen(false);
    setEditingCategory(null);
  };

  // 刪除處理
  const handleDelete = async (categoryId: number) => {
    try {
      setLoading(true);
      await categoryApi.deleteLargeCategory(categoryId);
      messageApi.success('刪除大分類成功');
      fetchData();
    } catch (error: any) {
      messageApi.error(error.message || '刪除大分類失敗');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {contextHolder}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-4">
          <BackButton to="/settings/categories" text="返回分類管理" />
        </div>

        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">大分類管理</h1>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showAddModal}
          >
            新增大分類
          </Button>
        </div>

        <DataTable<LargeCategoryListItem>
          columns={columns}
          dataSource={categories}
          loading={loading}
          total={totalCount}
          currentPage={currentPage}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onSort={handleSortChange}
          onFilter={handleFilterChange}
          rowKey="LargeCategoryId"
        />

        <CategoryFormModal
          isOpen={isModalOpen}
          modalType={modalType}
          editingCategory={editingCategory}
          defaultValues={defaultAddFormValues}
          onCancel={handleCancel}
          onSuccess={fetchData}
          messageApi={messageApi}
        />
      </div>
    </div>
  );
}

// 獨立的表單模態框元件
interface CategoryFormModalProps {
  isOpen: boolean;
  modalType: 'add' | 'edit';
  editingCategory: LargeCategoryDetail | null;
  defaultValues: Partial<CreateLargeCategoryDto>;
  onCancel: () => void;
  onSuccess: () => void;
  messageApi: any;
}

function CategoryFormModal({
  isOpen,
  modalType,
  editingCategory,
  defaultValues,
  onCancel,
  onSuccess,
  messageApi
}: CategoryFormModalProps) {
  const [form] = Form.useForm<CreateLargeCategoryDto | UpdateLargeCategoryDto>();
  const [loading, setLoading] = useState(false);

  // 當模態框打開且是編輯模式時，設置表單值
  useEffect(() => {
    if (isOpen) {
      if (modalType === 'edit' && editingCategory) {
        form.setFieldsValue({
          Name: editingCategory.Name,
          Description: editingCategory.Description,
          SortOrder: editingCategory.SortOrder,
          IsActive: editingCategory.IsActive,
        });
      } else {
        form.setFieldsValue(defaultValues as any);
      }
    }
  }, [isOpen, modalType, editingCategory, defaultValues, form]);

  // 提交表單
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (modalType === 'add') {
        await categoryApi.createLargeCategory(values as CreateLargeCategoryDto);
        messageApi.success('新增大分類成功');
      } else {
        if (!editingCategory) {
          messageApi.error('編輯資料不存在');
          return;
        }
        await categoryApi.updateLargeCategory(editingCategory.LargeCategoryId, values as UpdateLargeCategoryDto);
        messageApi.success('更新大分類成功');
      }

      form.resetFields();
      onCancel();
      onSuccess();
    } catch (error: any) {
      if (error.errorFields) {
        // 表單驗證錯誤
        return;
      }
      messageApi.error(error.message || `${modalType === 'add' ? '新增' : '更新'}大分類失敗`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={modalType === 'add' ? '新增大分類' : '編輯大分類'}
      open={isOpen}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          {modalType === 'add' ? '新增' : '更新'}
        </Button>,
      ]}
      width={600}
      destroyOnClose
    >
      {isOpen && (
        <Form
          form={form}
          layout="vertical"
          initialValues={defaultValues}
        >
          <Form.Item
            label="分類名稱"
            name="Name"
            rules={[
              { required: true, message: '請輸入分類名稱' },
              { max: 100, message: '分類名稱不能超過100個字元' }
            ]}
          >
            <Input placeholder="請輸入分類名稱" />
          </Form.Item>

          <Form.Item
            label="描述"
            name="Description"
            rules={[
              { max: 500, message: '描述不能超過500個字元' }
            ]}
          >
            <Input.TextArea 
              placeholder="請輸入描述（選填）" 
              rows={3}
            />
          </Form.Item>

          <Form.Item
            label="排序"
            name="SortOrder"
            rules={[
              { required: true, message: '請輸入排序數值' },
              { type: 'number', min: 0, message: '排序數值不能小於0' }
            ]}
          >
            <InputNumber 
              placeholder="請輸入排序數值" 
              min={0}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            label="狀態"
            name="IsActive"
            valuePropName="checked"
          >
            <Switch 
              checkedChildren="啟用" 
              unCheckedChildren="停用" 
            />
          </Form.Item>
        </Form>
      )}
    </Modal>
  );
} 