'use client';

import React from 'react';
import { Card, Row, Col, Button } from 'antd';
import { AppstoreOutlined, BranchesOutlined, NodeIndexOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';

export default function CategoriesPage() {
  const router = useRouter();

  const categoryTypes = [
    {
      title: '大分類管理',
      description: '管理系統中的大分類，作為分類體系的頂層結構',
      icon: <AppstoreOutlined className="text-4xl text-blue-500" />,
      path: '/settings/categories/large',
      color: 'border-blue-200 hover:border-blue-400'
    },
    {
      title: '中分類管理',
      description: '管理隸屬於大分類下的中分類，提供更細緻的分類層級',
      icon: <BranchesOutlined className="text-4xl text-green-500" />,
      path: '/settings/categories/medium',
      color: 'border-green-200 hover:border-green-400'
    },
    {
      title: '小分類管理',
      description: '管理最細緻的小分類，提供最詳細的分類選項',
      icon: <NodeIndexOutlined className="text-4xl text-orange-500" />,
      path: '/settings/categories/small',
      color: 'border-orange-200 hover:border-orange-400'
    }
  ];

  const handleNavigate = (path: string) => {
    router.push(path);
  };

  return (
    <div>
      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-2">分類管理</h1>
          <p className="text-gray-600">
            管理系統中的三級分類結構，包括大分類、中分類和小分類。請選擇要管理的分類類型。
          </p>
        </div>

        <Row gutter={[24, 24]}>
          {categoryTypes.map((category, index) => (
            <Col xs={24} sm={12} lg={8} key={index}>
              <Card
                className={`h-full transition-all duration-200 cursor-pointer ${category.color}`}
                hoverable
                onClick={() => handleNavigate(category.path)}
              >
                <div className="text-center">
                  <div className="mb-4">
                    {category.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">
                    {category.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                    {category.description}
                  </p>
                  <Button 
                    type="primary" 
                    block
                    onClick={(e) => {
                      e.stopPropagation();
                      handleNavigate(category.path);
                    }}
                  >
                    進入管理
                  </Button>
                </div>
              </Card>
            </Col>
          ))}
        </Row>

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="text-md font-semibold text-blue-800 mb-2">使用說明</h4>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• <strong>大分類</strong>：系統分類的頂層結構，例如「電子產品」、「服裝配件」等</li>
            <li>• <strong>中分類</strong>：隸屬於大分類下的次級分類，例如「手機」、「電腦」等</li>
            <li>• <strong>小分類</strong>：最細緻的分類層級，例如「智慧型手機」、「筆記型電腦」等</li>
            <li>• 建議按照 大分類 → 中分類 → 小分類 的順序建立分類體系</li>
            <li>• 刪除上層分類前，請先確認沒有下層分類依賴</li>
          </ul>
        </div>
      </div>
    </div>
  );
} 